/*
 * PacketCtrlSKY.cpp
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <string>
#include <vector>
#include <arpa/inet.h>
#include <iostream>
#include <sys/stat.h>
#include <errno.h>

using namespace std;

#include "PacketCtrlSKY.h"
//#include "Encrypt.h"
//#include "ksbase64.h"


int CPacketCtrlSKY::getMsg_BIND_REQ(char* pBuff, char* id, char* pw, int nTp)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN CONNECT\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "ID:%s\r\n", id);
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "PASSWORD:%s\r\n", pw);
	strcat(pBuff, strTemp);

	sprintf(strTemp, "REPORT:%s\r\n", nTp ? "Y" : "N");
	strcat(pBuff, strTemp);

	sprintf(strTemp, "VERSION:KSMSA/1.0.0\r\n");
	strcat(pBuff, strTemp);

	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_DELIVER_REQ(string& sSndBuff, SNDSKY& vtSend)
{
	char strTemp[LMS_TEXT_BUFF_SIZE];
	int nTxtCnt = 0;
	int nCtnCnt = 0;
	int nSize = 0;
	//char receiverNum[80];
	int size;
	//unsigned char* receiverNum64;
	char receiverNum[15+1];
	
	memset(strTemp, 0x00, sizeof(strTemp));
	//memset(receiverNum, 0x00, sizeof(receiverNum));
	
	sprintf(receiverNum, "%s", vtSend.s_tran_phone);
	
	sSndBuff = "BEGIN FTKUPGRADE\r\n";

	sprintf(strTemp, "KEY:%s\r\n", vtSend.s_tran_pr);				sSndBuff += strTemp;
	//sprintf(strTemp, "EXTEND:\r\n");									sSndBuff += strTemp;
	//sprintf(strTemp, "SUBJECT:\r\n");									sSndBuff += strTemp;
	sprintf(strTemp, "RECEIVER:%s\r\n", receiverNum);	sSndBuff += strTemp;
	//sprintf(strTemp, "SENDER:%s\r\n");								sSndBuff += strTemp;
	//sprintf(strTemp, "SENDER:%s\r\n", vtSend.s_tran_callback);		sSndBuff += strTemp;
	
	nCtnCnt = 1;
	nTxtCnt = 0;
	
	// sprintf(strTemp, "CONTENTCNT:%d\r\n", nCtnCnt);								     
	// sSndBuff += strTemp;
	// sprintf(strTemp, "PSKTYN:N\r\n");																	 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "PKTFYN:N\r\n");																	 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "PLGTYN:N\r\n");																	 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "TEXTCNT:0\r\n");																 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "IMGCNT:0\r\n");																	 
	// sSndBuff += strTemp;
	// //sprintf(strTemp, "AUTCNT:0\r\n");																	 
	// sprintf(strTemp, "AUDCNT:0\r\n");																	 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "MPCNT:0\r\n");																	 
	// sSndBuff += strTemp;
	// sprintf(strTemp, "BCASTYN:N\r\n");																 
	// sSndBuff += strTemp;

	sprintf(strTemp, "SENDERKEY:%s\r\n",vtSend.s_tran_sender_key); sSndBuff += strTemp;

	// Add ENCODING field
	if(strlen(trim(vtSend.s_encoding,strlen(vtSend.s_encoding))) > 0 ){
		sprintf(strTemp, "ENCODING:%s\r\n",vtSend.s_encoding);		 sSndBuff += strTemp;
	}

	// Add MESSAGE_TYPE field (using s_chat_bubble_type)
	if(strlen(trim(vtSend.s_chat_bubble_type,strlen(vtSend.s_chat_bubble_type))) > 0 ){
		sprintf(strTemp, "MESSAGE_TYPE:%s\r\n",vtSend.s_chat_bubble_type);		 sSndBuff += strTemp;
	}

	// Add TARGETING field
	if(strlen(trim(vtSend.s_targeting,strlen(vtSend.s_targeting))) > 0 ){
		sprintf(strTemp, "TARGETING:%s\r\n",vtSend.s_targeting);		 sSndBuff += strTemp;
	}

	sprintf(strTemp, "TMPLCD:%s\r\n",vtSend.s_tran_tmpl_cd);		 sSndBuff += strTemp;

	// Add PUSHALARM field
	if(strlen(trim(vtSend.s_push_alarm,strlen(vtSend.s_push_alarm))) > 0 ){
		sprintf(strTemp, "PUSHALARM:%s\r\n",vtSend.s_push_alarm);		 sSndBuff += strTemp;
	}

	// if(strlen(trim(vtSend.s_tran_button,strlen(vtSend.s_tran_button))) > 0 ){
	// 	sprintf(strTemp, "BUTTON:%s\r\n",vtSend.s_tran_button);		 sSndBuff += strTemp;
	// }

	// if(strlen(trim(vtSend.s_tran_title,strlen(vtSend.s_tran_title))) > 0 ){
	// 	sprintf(strTemp, "TITLE:%s\r\n",vtSend.s_tran_title);		 sSndBuff += strTemp;
	// }

	// if(strlen(trim(vtSend.s_tran_price,strlen(vtSend.s_tran_price))) > 0 ){
	// 	sprintf(strTemp, "PRICE:%s\r\n",vtSend.s_tran_price);		 sSndBuff += strTemp;
	// }

	// if(strlen(trim(vtSend.s_tran_curtype,strlen(vtSend.s_tran_curtype))) > 0 ){
	// 	sprintf(strTemp, "CURTYPE:%s\r\n",vtSend.s_tran_curtype);		 sSndBuff += strTemp;
	// }

	//if(strlen(trim(vtSend.s_tran_method,strlen(vtSend.s_tran_method))) > 0 ){
	//	sprintf(strTemp, "METHOD:%s\r\n",vtSend.s_tran_method);		 sSndBuff += strTemp;
	//}

	// if(strlen(trim(vtSend.s_tran_timeout,strlen(vtSend.s_tran_timeout))) > 0 ){
	// 	sprintf(strTemp, "TIMEOUT:%s\r\n",vtSend.s_tran_timeout);		 sSndBuff += strTemp;
	// }

	// Replace Flag : N / S / L
	//if (strlen(trim(vtSend.s_rep_flag, strlen(vtSend.s_rep_flag))) > 0) {
	//	sprintf(strTemp, "REPFLAG:%s\r\n", vtSend.s_rep_flag);		sSndBuff += strTemp;
	//}

	//if (strlen(trim(vtSend.s_rep_title, strlen(vtSend.s_rep_title))) > 0) {
	//	sprintf(strTemp, "REPTITLE:%s\r\n", vtSend.s_rep_title);		sSndBuff += strTemp;
	//}

	//if (strlen(trim(vtSend.s_tran_introlink, strlen(vtSend.s_tran_introlink))) > 0) {
	//	sprintf(strTemp, "INTROLINK:%s\r\n", vtSend.s_tran_introlink);	sSndBuff += strTemp;
	//}
	
	// if (strlen(trim(vtSend.s_reserved, strlen(vtSend.s_reserved))) > 0) {
	// 	sprintf(strTemp, "RESERVED:%s\r\n", vtSend.s_reserved);	sSndBuff += strTemp;
	// }

	cout<<"s_tran_tmpl_cd:"<<vtSend.s_tran_tmpl_cd<<"\n"<<endl;
	cout<<"s_tran_button:"<<vtSend.s_tran_button<<"\n"<<endl;
	cout<<"s_tran_title:"<<vtSend.s_tran_title<<"\n"<<endl;
	cout<<"s_tran_price:"<<vtSend.s_tran_price<<"\n"<<endl;
	cout<<"s_tran_curtype:"<<vtSend.s_tran_curtype<<"\n"<<endl;
	cout<<"s_tran_method:"<<vtSend.s_tran_method<<"\n"<<endl;
	cout<<"s_tran_timeout:"<<vtSend.s_tran_timeout<<"\n"<<endl;
	cout<<"s_rep_flag:"<<vtSend.s_rep_flag<<"\n"<<endl;
	cout<<"s_rep_title:"<<vtSend.s_rep_title<<"\n"<<endl;
	cout<<"s_tran_introlink:"<<vtSend.s_tran_introlink<<"\n"<<endl;
	cout<<"s_reserved:"<<vtSend.s_reserved<<"\n"<<endl;
	
	/*
mmsFileProcess.cpp:    char* this->m_ptrData;
mmsFileProcess.cpp:    this->m_ptrData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), 


    this->m_ptrData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(),
            mData.strData.length(), &size);*/

	// TXT Data
            
	sprintf(strTemp, "MDATA:MIME-Version: 1.0\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:Content-Type:multipart/mixed;boundary=\"----=_NextPart_000_0049_01C851E9.827BA430\"\r\n");	sSndBuff += strTemp;
	//sprintf(strTemp, "MDATA: boundary=\"----=_NextPart_000_0049_01C851E9.827BA430\"\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:\r\n");	sSndBuff += strTemp;

	sprintf(strTemp, "MDATA:------=_NextPart_000_0049_01C851E9.827BA430\r\n");	sSndBuff += strTemp;
	//sprintf(strTemp, "MDATA:Content-Type:text/plain;charset=\"ks_c_5601-1987\"\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:Content-Type: TXT;charset=\"ks_c_5601-1987\"\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:Content-Transfer-Encoding:base64\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:Content-Svc:ALL;\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:Content-Seq:1;\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:\r\n");					sSndBuff += strTemp;
#if (DEBUG >= 5)
	cout<<"s_tran_msg:["<<vtSend.s_tran_msg << "]" <<  endl;
#endif
	
	//this->m_ptrData = (char*)__base64_encode((unsigned char *)vtSend.s_tran_msg, sizeof(vtSend.s_tran_msg), &nSize);
	this->m_ptrData = (char*)__base64_encode((unsigned char *)vtSend.s_tran_msg, strlen(vtSend.s_tran_msg), &nSize);

	//sprintf(strTemp, "MDATA:%s\r\n", string(*(itrData+3)).c_str());	sSndBuff += strTemp;
	//sprintf(strTemp, "MDATA:%s\r\n", this->m_ptrData);			sSndBuff += strTemp;
#if (DEBUG >= 5)
	cout<<"m_ptrData:["<<this->m_ptrData << "]" <<  endl;
	cout<<"m_ptrData size :["<< nSize << "]" <<  endl;
#endif
	
	sSndBuff += "MDATA:";
	sSndBuff += this->m_ptrData;
	sSndBuff += "\r\n";
	
	if(this->m_ptrData != NULL) free(this->m_ptrData);
	
	sprintf(strTemp, "MDATA:\r\n");			sSndBuff += strTemp;

	/* Replace Msg Text		*/
	if (strlen(trim(vtSend.s_rep_msg, strlen(vtSend.s_rep_msg))) > 0) {
		sprintf(strTemp, "MDATA:------=_NextPart_000_0049_01C851E9.827BA430\r\n");	sSndBuff += strTemp;
		sprintf(strTemp, "MDATA:Content-Type: REPTXT;charset=\"ks_c_5601-1987\"\r\n");	sSndBuff += strTemp;
		sprintf(strTemp, "MDATA:Content-Transfer-Encoding:base64\r\n");	sSndBuff += strTemp;
		sprintf(strTemp, "MDATA:Content-Svc:ALL;\r\n");	sSndBuff += strTemp;
		sprintf(strTemp, "MDATA:Content-Seq:1;\r\n");	sSndBuff += strTemp;
		sprintf(strTemp, "MDATA:\r\n");					sSndBuff += strTemp;
		//this->m_ptrData = (char*)__base64_encode((unsigned char *)vtSend.s_rep_msg, sizeof(vtSend.s_rep_msg), &nSize);
		this->m_ptrData = (char*)__base64_encode((unsigned char *)vtSend.s_rep_msg, strlen(vtSend.s_rep_msg), &nSize);
		sSndBuff += "MDATA:";
		sSndBuff += this->m_ptrData;
		sSndBuff += "\r\n";
		if(this->m_ptrData != NULL) free(this->m_ptrData);
		sprintf(strTemp, "MDATA:\r\n");			sSndBuff += strTemp;
	}

	sprintf(strTemp, "MDATA:------=_NextPart_000_0049_01C851E9.827BA430\r\n");	sSndBuff += strTemp;
	sprintf(strTemp, "MDATA:\r\n");				sSndBuff += strTemp;
	sprintf(strTemp, "END\r\n");				sSndBuff += strTemp;
	
	cout<<"sSndBuff:"<<sSndBuff.c_str()<<endl;
	
	return sSndBuff.length();

}

int CPacketCtrlSKY::getMsg_PING_REQ(char* pBuff)
{
	char strTemp[100];

	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN PING\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:100\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_PING_RES(char* pBuff, string sKey)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN PONG\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:%s\r\n", sKey.c_str());
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_REPORT_ACK(char* pBuff, string sKey)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN ACK\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:%s\r\n", sKey.c_str());
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getData_BndAck(char* pBuff, vector<string>& vtBndAck)
{
	string strCode;
	string strDesc;
	
	matchString(pBuff, "CODE", strCode);
	matchString(pBuff, "DESC", strDesc);
	
	try {
		vtBndAck.push_back(strCode);
		vtBndAck.push_back(strDesc);
	}
	catch (...) {
		return -1;
	}
	return 1;
}


int CPacketCtrlSKY::getData_SndAck(char* pBuff, vector<string>& vtSndAck)
{
	string strKey;
	string strCode;
	string strDesc;
	
	matchString(pBuff, "KEY", strKey);
	matchString(pBuff, "CODE", strCode);
	matchString(pBuff, "DESC", strDesc);
	
	cout<<"getData_SndAck pBuff:"<<pBuff<<"\n"<<endl;
	cout<<"getData_SndAck strKey:"<<strKey<<"\n"<<endl;
	cout<<"getData_SndAck strCode:"<<strCode<<"\n"<<endl;
	cout<<"getData_SndAck strDesc:"<<strDesc<<"\n"<<endl;
	
	try {
		vtSndAck.push_back(strKey);
		vtSndAck.push_back(strCode);
		vtSndAck.push_back(strDesc);
	}
	catch (...) {
		return -1;
	}
	return 1;
	
}

int CPacketCtrlSKY::getData_Report(char* pBuff, vector<string>& vtReport)
{
	string strKey;
	string strCode;
	string strTime;
	string strDesc;
	string strNet;
	
/*
pbuff Report :BEGIN REPORT
KEY:10972
CODE:1000            
TIME:20131029195455
DESC:succ
NET:KTF
END
*/
	try {	
  	if(strstr(pBuff, "BEGIN REPORT")){
				cout<<"BEGIN REPORT :"<<pBuff<<endl;
				matchString(pBuff, "KEY", strKey);
				matchString(pBuff, "CODE", strCode);
				matchString(pBuff, "TIME", strTime);
				matchString(pBuff, "DESC", strDesc);
  				matchString(pBuff, "NET", strNet);
				
				vtReport.push_back(strKey);
				vtReport.push_back(strCode);
				vtReport.push_back(strTime);
				vtReport.push_back(strDesc);
				vtReport.push_back(strNet);
				return 1;
			}
		else if(strstr(pBuff, "BEGIN PING")){
				matchString(pBuff, "KEY", strKey);
				
				vtReport.push_back(strKey);
				return 2;
		}
		else{
				vtReport.push_back("BEGIN UNKNOWN");
				return 3;
		}  	
	}
	catch (...) {
		return -1;
	}
	return 1;
	
}

int CPacketCtrlSKY::getMsgCode(char* pBuff)
{
	HEADER *head = (HEADER*)pBuff;
	int nType = -1;
	try {
	//	nType = atoi(head->msgType);
	//	nType = head->msgType;
			nType = ntohl(head->msgType);
	}
	catch (...) {
		nType = -1;
	}
	return nType;
}



char* CPacketCtrlSKY::matchString(char* szOrg, char* szTag, string &strVal)
{
        #ifdef DEBUG
    //    printf("CPacketCtrlSKY::matchString:szOrg:[%s]\n",szOrg);
    //    printf("CPacketCtrlSKY::matchString:szTag:[%s]\n",szTag);
    //    printf("CPacketCtrlSKY::matchString:strVal:[%s]\n",strVal.c_str());
        #endif
        char* szStt = NULL;
        char* szEnd = NULL;
        strVal = "";
        strVal.reserve(0);
        if(szOrg == NULL)
        {
                return NULL;//??어??값이 ??으????연??NULL
        }

        if( (szStt = strstr(szOrg,szTag)) )  // szTag 값을 szOrg??서 찾아 ??인??값??반환??여 szStt??????        
		{
                szStt = szStt+strlen(szTag)+1;   // TAG값을 ??외??기??헤 문자??shift
                szEnd = strstr(szStt,"\r\n");   // ??인 구분??의 ??인??값??구함
                if((szEnd - szStt) == 0)        // ??료가 ??을 경우 
                {
                        strVal = "";
                        strVal.reserve(0);
                }
                else                            // ??료가 ??을 경우
                {
                        strVal = "";
                        strVal.reserve(0);
                        if(szEnd == NULL)
                        {

                                strVal.insert(0,szStt);
                        }
                        else
                        {
                                strVal.insert(0,szStt, szEnd-szStt);
                        }
                        #ifdef DEBUG
                   //     printf("CPacketCtrl::matchString:strVal:[%s]\n",strVal.c_str());
                        #endif
                }
        }
        if(szStt == NULL)
        {
                return NULL;
        }
        if(szEnd == NULL)
        {
                return NULL;
        }

        #ifdef DEBUG
    //    printf("CPacketCtrl::matchString:END:[%s]\n",szEnd+2);
        #endif

        return szEnd+2;
}


char* CPacketCtrlSKY::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}
