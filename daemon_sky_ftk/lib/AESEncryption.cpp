#include "AESEncryption.h"
#include "base64.h"
#include <fstream>
#include <iostream>
#include <cstring>
#include <cstdlib>

AESEncryption::AESEncryption() : keyLoaded(false) {
    memset(key, 0, sizeof(key));
}

AESEncryption::~AESEncryption() {
    // Clear sensitive data
    memset(key, 0, sizeof(key));
}

bool AESEncryption::loadKeyFromFile(const std::string& keyFilePath) {
    std::ifstream file(keyFilePath.c_str());
    if (!file.is_open()) {
        std::cerr << "Failed to open key file: " << keyFilePath << std::endl;
        return false;
    }
    
    std::string hexKey;
    std::getline(file, hexKey);
    file.close();
    
    // Remove any whitespace
    hexKey.erase(std::remove_if(hexKey.begin(), hexKey.end(), ::isspace), hexKey.end());
    
    return setKey(hexKey);
}

bool AESEncryption::setKey(const std::string& hexKey) {
    if (hexKey.length() != 32) {
        std::cerr << "Key must be exactly 32 hex characters (128 bits)" << std::endl;
        return false;
    }
    
    if (!hexStringToBytes(hexKey, key, 16)) {
        std::cerr << "Invalid hex key format" << std::endl;
        return false;
    }
    
    keyLoaded = true;
    return true;
}

std::string AESEncryption::encryptAndEncode(const std::string& plaintext) {
    if (!keyLoaded) {
        std::cerr << "Encryption key not loaded" << std::endl;
        return "";
    }
    
    // Generate random IV (16 bytes for AES)
    unsigned char iv[AES_BLOCK_SIZE];
    if (RAND_bytes(iv, AES_BLOCK_SIZE) != 1) {
        std::cerr << "Failed to generate random IV" << std::endl;
        return "";
    }
    
    // Prepare input data
    const unsigned char* input = reinterpret_cast<const unsigned char*>(plaintext.c_str());
    size_t inputLen = plaintext.length();
    
    // Allocate output buffer (same size as input for CTR mode)
    unsigned char* output = new unsigned char[inputLen];
    
    // Set up AES key
    AES_KEY aesKey;
    if (AES_set_encrypt_key(key, 128, &aesKey) < 0) {
        delete[] output;
        std::cerr << "Failed to set AES key" << std::endl;
        return "";
    }
    
    // Encrypt using AES-CTR
    unsigned char ivec[AES_BLOCK_SIZE];
    unsigned char ecount_buf[AES_BLOCK_SIZE];
    unsigned int num = 0;
    
    memcpy(ivec, iv, AES_BLOCK_SIZE);
    memset(ecount_buf, 0, AES_BLOCK_SIZE);
    
    AES_ctr128_encrypt(input, output, inputLen, &aesKey, ivec, ecount_buf, &num);
    
    // Combine IV + encrypted data
    size_t totalLen = AES_BLOCK_SIZE + inputLen;
    unsigned char* combined = new unsigned char[totalLen];
    memcpy(combined, iv, AES_BLOCK_SIZE);
    memcpy(combined + AES_BLOCK_SIZE, output, inputLen);
    
    // Encode to base64
    std::string result = bytesToBase64(combined, totalLen);
    
    // Clean up
    delete[] output;
    delete[] combined;
    
    return result;
}

std::string AESEncryption::decodeAndDecrypt(const std::string& base64Ciphertext) {
    if (!keyLoaded) {
        std::cerr << "Encryption key not loaded" << std::endl;
        return "";
    }
    
    // Decode from base64
    unsigned char* combined;
    size_t combinedLen;
    if (!base64ToBytes(base64Ciphertext, &combined, &combinedLen)) {
        std::cerr << "Failed to decode base64" << std::endl;
        return "";
    }
    
    if (combinedLen < AES_BLOCK_SIZE) {
        free(combined);
        std::cerr << "Invalid ciphertext length" << std::endl;
        return "";
    }
    
    // Extract IV and encrypted data
    unsigned char* iv = combined;
    unsigned char* ciphertext = combined + AES_BLOCK_SIZE;
    size_t ciphertextLen = combinedLen - AES_BLOCK_SIZE;
    
    // Allocate output buffer
    unsigned char* output = new unsigned char[ciphertextLen + 1];
    
    // Set up AES key
    AES_KEY aesKey;
    if (AES_set_encrypt_key(key, 128, &aesKey) < 0) {
        delete[] output;
        free(combined);
        std::cerr << "Failed to set AES key" << std::endl;
        return "";
    }
    
    // Decrypt using AES-CTR
    unsigned char ivec[AES_BLOCK_SIZE];
    unsigned char ecount_buf[AES_BLOCK_SIZE];
    unsigned int num = 0;
    
    memcpy(ivec, iv, AES_BLOCK_SIZE);
    memset(ecount_buf, 0, AES_BLOCK_SIZE);
    
    AES_ctr128_encrypt(ciphertext, output, ciphertextLen, &aesKey, ivec, ecount_buf, &num);
    
    // Null terminate
    output[ciphertextLen] = '\0';
    
    std::string result(reinterpret_cast<char*>(output));
    
    // Clean up
    delete[] output;
    free(combined);
    
    return result;
}

bool AESEncryption::hexStringToBytes(const std::string& hex, unsigned char* bytes, size_t maxLen) {
    if (hex.length() % 2 != 0 || hex.length() / 2 > maxLen) {
        return false;
    }
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        char* endPtr;
        unsigned long byte = strtoul(byteString.c_str(), &endPtr, 16);
        
        if (*endPtr != '\0' || byte > 255) {
            return false;
        }
        
        bytes[i / 2] = static_cast<unsigned char>(byte);
    }
    
    return true;
}

std::string AESEncryption::bytesToBase64(const unsigned char* data, size_t len) {
    int encodedLen = Base64encode_len(len);
    char* encoded = new char[encodedLen];
    
    int actualLen = Base64encode(encoded, reinterpret_cast<const char*>(data), len);
    
    std::string result(encoded, actualLen - 1); // -1 to exclude null terminator
    delete[] encoded;
    
    return result;
}

bool AESEncryption::base64ToBytes(const std::string& base64, unsigned char** data, size_t* len) {
    // Estimate decoded length
    size_t estimatedLen = (base64.length() * 3) / 4;
    char* decoded = new char[estimatedLen + 1];
    
    int actualLen = Base64decode(decoded, base64.c_str());
    if (actualLen < 0) {
        delete[] decoded;
        return false;
    }
    
    *data = reinterpret_cast<unsigned char*>(malloc(actualLen));
    if (*data == NULL) {
        delete[] decoded;
        return false;
    }
    
    memcpy(*data, decoded, actualLen);
    *len = actualLen;
    
    delete[] decoded;
    return true;
}
